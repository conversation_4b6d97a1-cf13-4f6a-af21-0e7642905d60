"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Upload, Link, FileText } from "lucide-react";
import { toast } from "sonner";

interface PDFUploadProps {
  onFileSelect: (file: string | File) => void;
}

export default function PDFUpload({ onFileSelect }: PDFUploadProps) {
  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === "application/pdf") {
      onFileSelect(file);
    } else {
      toast.error("Invalid file type", {
        description: "Please select a PDF file.",
      });
    }
  };

  const handleUrlSubmit = async () => {
    if (!url) return;

    setIsLoading(true);
    try {
      // Validate URL format
      new URL(url);
      onFileSelect(url);
    } catch {
      toast.error("Invalid URL", {
        description: "Please enter a valid URL.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type === "application/pdf") {
      onFileSelect(file);
    } else {
      toast.error("Invalid file type", {
        description: "Please drop a PDF file.",
      });
    }
  };

  return (
    <div className="container mx-auto mobile-padding lg:px-4 py-4 lg:py-8 max-w-2xl safe-area-top safe-area-bottom">
      <div className="text-center mb-6 lg:mb-8">
        <FileText className="mx-auto h-12 w-12 lg:h-16 lg:w-16 text-primary mb-3 lg:mb-4" />
        <h1 className="text-2xl lg:text-4xl font-bold mb-2">PDF Viewer</h1>
        <p className="text-muted-foreground text-base lg:text-lg">
          Upload a PDF file or provide a URL to get started
        </p>
        <p className="text-xs lg:text-sm text-muted-foreground mt-2">
          ✨ Features: Navigation • Zoom • Search • Bookmarks
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-2 touch-target">
          <TabsTrigger value="upload" className="touch-target">Upload File</TabsTrigger>
          <TabsTrigger value="url" className="touch-target">From URL</TabsTrigger>
        </TabsList>

        <TabsContent value="upload">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload PDF File
              </CardTitle>
              <CardDescription>
                Select a PDF file from your device
              </CardDescription>
            </CardHeader>
            <CardContent className="mobile-padding lg:p-6">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg mobile-padding lg:p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer touch-manipulation min-h-[120px] lg:min-h-[160px] flex flex-col items-center justify-center"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={() => document.getElementById("file-input")?.click()}
                role="button"
                tabIndex={0}
                aria-label="Upload PDF file"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    document.getElementById("file-input")?.click();
                  }
                }}
              >
                <Upload className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-muted-foreground mb-3 lg:mb-4" />
                <p className="text-base lg:text-lg font-medium mb-2">
                  Drop your PDF here or tap to browse
                </p>
                <p className="text-xs lg:text-sm text-muted-foreground">
                  Supports PDF files up to 50MB
                </p>
                <input
                  id="file-input"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="url">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Load from URL
              </CardTitle>
              <CardDescription>
                Enter a direct link to a PDF file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 mobile-padding lg:p-6">
              <Input
                type="url"
                placeholder="https://example.com/document.pdf"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleUrlSubmit()}
                className="touch-target"
                aria-label="PDF URL"
              />
              <Button
                onClick={handleUrlSubmit}
                disabled={!url || isLoading}
                className="w-full touch-target-comfortable"
                aria-label={isLoading ? "Loading PDF" : "Load PDF from URL"}
              >
                {isLoading ? "Loading..." : "Load PDF"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
