"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Document, pdfjs } from 'react-pdf';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// Import our optimized components
import CompactHeader from '../navigation/compact-header';
import OptimizedSidebar, { SidebarMode } from '../navigation/optimized-sidebar';
import MultiPDFLayoutManager, { LayoutMode, LayoutConfig } from '../layout/multi-pdf-layout-manager';
import PDFSimplePage from './pdf-simple-page';

// Import existing components we'll reuse
import PDFSearch from '../search/pdf-search';
import PDFBookmarks from '../navigation/pdf-bookmarks';
import PDFOutline from '../navigation/pdf-outline';
import PDFThumbnailView from '../navigation/pdf-thumbnail-view';
import PDFAnnotations from '../annotations/pdf-annotations';

import type { DocumentInstance } from '@/lib/types/pdf';
import type { Annotation } from '../annotations/pdf-annotations';

// Configure PDF.js worker
import { configurePDFWorker, handlePDFError } from '@/lib/pdf-worker-config';

// Initialize PDF.js worker configuration
configurePDFWorker();

interface OptimizedPDFViewerProps {
  documents: DocumentInstance[];
  activeDocumentId: string | null;
  onDocumentSelect: (documentId: string) => void;
  onDocumentClose: (documentId: string) => void;
  onClose: () => void;
  className?: string;
}

export default function OptimizedPDFViewer({
  documents,
  activeDocumentId,
  onDocumentSelect,
  onDocumentClose,
  onClose,
  className
}: OptimizedPDFViewerProps) {
  // Layout state
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarMode, setSidebarMode] = useState<SidebarMode>('compact');
  const [activeTab, setActiveTab] = useState('outline');
  const [layoutConfig, setLayoutConfig] = useState<LayoutConfig>({
    mode: 'single',
    gridColumns: 2,
    gridRows: 2,
    splitRatio: 0.5,
    overlayOpacity: 0.7,
    pipPosition: 'bottom-right',
    pipSize: 'medium',
    showBorders: true,
    syncScroll: false,
    syncZoom: false,
    autoArrange: true,
  });

  // PDF state for active document
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Content state
  const [searchText, setSearchText] = useState('');
  const [bookmarks, setBookmarks] = useState<any[]>([]);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [outline, setOutline] = useState<any[]>([]);

  const containerRef = useRef<HTMLDivElement>(null);

  // Get active document
  const activeDocument = documents.find(doc => doc.id === activeDocumentId);

  // Handle document load success
  const onDocumentLoadSuccess = useCallback((pdf: any) => {
    setPdfDocument(pdf);
    setNumPages(pdf.numPages);
    setPageNumber(1);
    setIsLoading(false);

    // Load outline
    pdf.getOutline().then((outline: any) => {
      setOutline(outline || []);
    });
  }, []);

  // Handle document load error
  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('Error loading PDF:', error);
    setIsLoading(false);

    // Use enhanced error handling
    const errorInfo = handlePDFError(error);

    toast.error('Failed to load PDF document', {
      description: `${errorInfo.message}. ${errorInfo.suggestedAction}`,
    });
  }, []);

  // Navigation functions
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
    }
  }, [numPages]);

  const goToPrevPage = useCallback(() => {
    goToPage(pageNumber - 1);
  }, [pageNumber, goToPage]);

  const goToNextPage = useCallback(() => {
    goToPage(pageNumber + 1);
  }, [pageNumber, goToPage]);

  // Zoom functions
  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 3.0));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.5));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, []);

  // Rotation function
  const rotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  // Fullscreen toggle
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Bookmark functions
  const isPageBookmarked = useCallback((page: number) => {
    return bookmarks.some(bookmark => bookmark.page === page);
  }, [bookmarks]);

  const addBookmark = useCallback(() => {
    if (!isPageBookmarked(pageNumber)) {
      const newBookmark = {
        id: Date.now().toString(),
        page: pageNumber,
        title: `Page ${pageNumber}`,
        timestamp: new Date().toISOString()
      };
      setBookmarks(prev => [...prev, newBookmark]);
      toast.success('Bookmark added');
    }
  }, [pageNumber, isPageBookmarked]);

  // Download function
  const downloadPDF = useCallback(() => {
    if (activeDocument) {
      const link = document.createElement('a');
      link.href = typeof activeDocument.file === 'string' ? activeDocument.file : URL.createObjectURL(activeDocument.file);
      link.download = activeDocument.title;
      link.click();
    }
  }, [activeDocument]);

  // Sidebar functions
  const openSidebar = useCallback((tab?: string) => {
    setSidebarOpen(true);
    if (tab) {
      setActiveTab(tab);
    }
  }, []);

  // Prepare sidebar tabs
  const sidebarTabs = [
    {
      id: 'outline',
      label: 'Outline',
      icon: ({ className }: { className?: string }) => <div className={className}>📋</div>,
      content: <PDFOutline outline={outline} />,
      priority: 'high' as const
    },
    {
      id: 'search',
      label: 'Search',
      icon: ({ className }: { className?: string }) => <div className={className}>🔍</div>,
      content: (
        <PDFSearch
          searchText={searchText}
          onSearchChange={setSearchText}
          onClose={() => {}}
          pdfDocument={pdfDocument}
          numPages={numPages}
          onPageSelect={goToPage}
          onSearchResults={() => {}}
          onCurrentSearchIndex={() => {}}
        />
      ),
      priority: 'high' as const
    },
    {
      id: 'bookmarks',
      label: 'Bookmarks',
      icon: ({ className }: { className?: string }) => <div className={className}>🔖</div>,
      content: (
        <PDFBookmarks
          bookmarks={bookmarks}
          currentPage={pageNumber}
          onPageSelect={goToPage}
          onAddBookmark={addBookmark}
          onRemoveBookmark={(id) => setBookmarks(prev => prev.filter(b => b.id !== id))}
          onUpdateBookmark={(id, updates) => setBookmarks(prev => prev.map(b => b.id === id ? { ...b, ...(updates as any) } : b))}
        />
      ),
      priority: 'medium' as const
    },
    {
      id: 'thumbnails',
      label: 'Thumbnails',
      icon: ({ className }: { className?: string }) => <div className={className}>🖼️</div>,
      content: (
        <PDFThumbnailView
          pdfDocument={pdfDocument}
          numPages={numPages}
          currentPage={pageNumber}
          onPageSelect={goToPage}
          scale={0.2}
          viewMode="list"
        />
      ),
      priority: 'medium' as const
    },
    {
      id: 'annotations',
      label: 'Annotations',
      icon: ({ className }: { className?: string }) => <div className={className}>✏️</div>,
      content: <div>Annotations panel</div>,
      priority: 'low' as const
    }
  ];

  // Render single PDF document
  const renderDocument = useCallback((document: DocumentInstance, containerProps?: any) => {
    if (!document) return null;

    return (
      <div className={cn("flex items-center justify-center h-full", containerProps?.className)}>
        <Card className="shadow-none border-0 w-full max-w-full bg-transparent">
          <Document
            file={document.file}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            }
            className="max-w-full"
          >
            <PDFSimplePage
              pageNumber={pageNumber}
              scale={containerProps?.scale || scale}
              rotation={rotation}
              className="shadow-sm"
              pdfDocument={pdfDocument}
              searchText={searchText}
              annotations={annotations}
              selectedTool={null}
              selectedColor="#FFEB3B"
              onAnnotationAdd={() => {}}
              formFields={[]}
              formData={{}}
              onFormDataChange={() => {}}
              isFormDesignMode={false}
              enableAnnotations={true}
              enableForms={true}
              enableTextSelection={true}
              enableSearch={true}
              enableContextMenu={false}
            />
          </Document>
        </Card>
      </div>
    );
  }, [
    pageNumber,
    scale,
    rotation,
    pdfDocument,
    searchText,
    annotations,
    onDocumentLoadSuccess,
    onDocumentLoadError
  ]);

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <div ref={containerRef} className={cn("h-screen flex flex-col bg-background", className)}>
      {/* Optimized Sidebar */}
      <OptimizedSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        mode={sidebarMode}
        onModeChange={setSidebarMode}
        tabs={sidebarTabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isPinned={false}
        autoHide={true}
        autoHideDelay={3000}
      />

      {/* Compact Header */}
      <CompactHeader
        title={activeDocument?.title}
        pageNumber={pageNumber}
        numPages={numPages}
        scale={scale}
        rotation={rotation}
        isFullscreen={isFullscreen}
        onPageChange={goToPage}
        onPrevPage={goToPrevPage}
        onNextPage={goToNextPage}
        canGoToPrev={pageNumber > 1}
        canGoToNext={pageNumber < numPages}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        onResetZoom={resetZoom}
        onRotate={rotate}
        onToggleFullscreen={toggleFullscreen}
        onClose={onClose}
        onDownload={downloadPDF}
        onOpenSidebar={openSidebar}
        selectedTool={null}
        onToolSelect={() => {}}
        isPageBookmarked={isPageBookmarked(pageNumber)}
        onAddBookmark={addBookmark}
        isCompact={true}
        showAdvancedTools={false}
      />

      {/* Main Content Area with Multi-PDF Layout */}
      <div className="flex-1 min-h-0">
        <MultiPDFLayoutManager
          documents={documents}
          activeDocumentId={activeDocumentId}
          layoutConfig={layoutConfig}
          onLayoutConfigChange={setLayoutConfig}
          onDocumentSelect={onDocumentSelect}
          renderDocument={renderDocument}
        />
      </div>

      {/* Minimal Status Bar */}
      {activeDocument && (
        <div className="border-t px-3 py-1 bg-muted/20 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>
              {documents.length} document{documents.length !== 1 ? 's' : ''} • {layoutConfig.mode} view
            </span>
            <span>
              Page {pageNumber}/{numPages} • {Math.round(scale * 100)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
